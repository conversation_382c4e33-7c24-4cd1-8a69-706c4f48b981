defmodule ExServiceClient.Clients.BaseClient do
  @moduledoc false
  @spec __using__(any) :: {:__block__, [], [{:@, [...], [...]} | {:def, [...], [...]} | {:defp, [...], [...]}, ...]}
  defmacro __using__(_) do
    quote do
      @doc """
      Build a dynamic client from a given config.
      """
      @version Mix.Project.config()[:version]

      @spec client(list()) :: Tesla.Client.t()
      def client(opts \\ []) do
        user_token = Keyword.get(opts, :authorization, nil)
        environment = Keyword.get(config(), :environment, :prod)
        timeout = Keyword.get(opts, :timeout, 30_000)
        experimental? = Keyword.get(opts, :experimental, false)

        headers = [
          {"user-agent", "SD service-client v#{inspect(@version)}"},
          {"x-api-experimental", to_string(experimental?)}
        ]

        middleware = [
          {Tesla.Middleware.OpenTelemetry,
           [
             span_name: "#{inspect(__MODULE__)}.request",
             span_attributes: [
               %{key: "service.version", value: @version},
               %{key: "service.name", value: "ex_service_client"}
             ]
           ]},
          {Tesla.Middleware.JSON, decode: fn body -> {:ok, body} end, encode: fn body -> Poison.encode(body) end},
          {Tesla.Middleware.Logger, debug: true},
          {Tesla.Middleware.Timeout, timeout: timeout},
          {Tesla.Middleware.Headers, headers},
          {Tesla.Middleware.Retry, max_retries: 3, delay: 500, max_delay: 10_000},
          Tesla.Middleware.FollowRedirects
        ]

        middleware =
          case environment do
            :minikube -> [{Tesla.Middleware.BaseUrl, base_url(:local, opts)} | middleware]
            :local -> [{Tesla.Middleware.BaseUrl, base_url(:local, opts)} | middleware]
            :dev -> [{Tesla.Middleware.BaseUrl, base_url(:prod, opts)} | middleware]
            :prod -> [{Tesla.Middleware.BaseUrl, base_url(:prod, opts)} | middleware]
            _ -> middleware
          end

        middleware =
          case user_token do
            nil -> [{Tesla.Middleware.Headers, ["x-api-token": get_auth_key()]} | middleware]
            _ -> [{Tesla.Middleware.BearerAuth, [token: user_token]} | middleware]
          end

        Tesla.client(middleware, Tesla.Adapter.Hackney)
      end

      defp base_url(:local, opts) do
        endpoint = Keyword.get(opts, :endpoint, :backendV2)

        case endpoint do
          :accounts -> Keyword.get(config(), :accounts_url, "api.sdlab/accounts/api")
          :events -> "localhost:4000/events/api"
          :orders -> Keyword.get(config(), :orders_url, "orders-service:4000/orders/api")
          :coms -> Keyword.get(config(), :coms_url, "com-service:4000/coms/api")
          :pdf -> Keyword.get(config(), :pdf_url, "pdf-service:4000/pdf/api")
          :shortlink -> "http://link.dev.stagedat.es"
        end
      end

      defp base_url(:prod, opts) do
        endpoint = Keyword.get(opts, :endpoint, :backendV2)
        backend_endpoint = Keyword.get(config(), :backend_endpoint, "https://stagedates.com")
        gke_endpoint = Keyword.get(config(), :gke_endpoint, "https://api.stagedates.com")

        case endpoint do
          :backendV2 -> "#{backend_endpoint}/api"
          :accounts -> "api.sdlab/accounts/api"
          :events -> "localhost:4000/events/api"
          :orders -> "#{backend_endpoint}/orders/api"
          :coms -> "#{backend_endpoint}/api/coms"
          :pdf -> "#{gke_endpoint}/pdf/api"
          :shortlink -> Keyword.get(config(), :shortlink_service_base_url, "https://link.stagedates.com")
        end
      end

      def config do
        Application.get_all_env(:ex_service_client)
      end

      def parse_response(response, decode_fn \\ &decode_str/1) do
        case response do
          {:ok, %Tesla.Env{status: status, body: body}} when status < 300 ->
            {:ok, decode_fn.(body)}

          {:ok, %Tesla.Env{status: status, body: body}} when status < 400 ->
            {:ok, decode_str(body)}

          {:ok, %Tesla.Env{status: status, body: body}} when status < 600 ->
            {:error, %{message: decode_str(body), status: status}}

          {:error, error} ->
            {:error, error}
        end
      end

      defp decode_str(""), do: nil

      defp decode_str(data) do
        case Poison.decode(data) do
          {:ok, decoded_data} -> decoded_data
          {:error, _} -> data
        end
      end

      defp get_auth_key do
        ExServiceClient.JwtTokenGenServer.value()
      end
    end
  end
end
