package handlers

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/stagedates/shortlink-service/pkg/validators"
	"gofr.dev/pkg/gofr"
)

type DynamicLink struct {
	ID         uuid.UUID `json:"id"`
	SrcURL     string    `json:"shortlink"`
	TargetURL  string    `json:"target_url"`
	InsertedAt time.Time `json:"inserted_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// FirebaseLinkCreationHandler creates a new link in the database
// based on the firebase dynamic link specification found here:
// https://firebase.google.com/docs/dynamic-links/rest
// Target url example "https://stagedates.com/events/star-fm-rooftop-party-nhow-berlin-20250726-vDO1Q"
func FirebaseLinkCreationHandler(app *gofr.App) func(*gofr.Context) (any, error) {
	return func(ctx *gofr.Context) (any, error) {
		var (
			link      DynamicLink
			_srcURL   string
			srcURL    string
			targetUrl string
		)

		if err := ctx.Bind(&link); err != nil {
			return nil, err
		}

		ctx.Logger.Debugf("inserting link: %+v", link)

		// Extract API key from context
		requestApiKeyRaw := ctx.Value("RequestApiKey")

		// Convert to string safely
		requestApiKey := ""
		if requestApiKeyRaw != nil {
			if apiKeyStr, ok := requestApiKeyRaw.(string); ok {
				requestApiKey = apiKeyStr
			}
		}

		if !validators.ApiKeyValidator(app, requestApiKey) {
			ctx.Logger.Warn("Unauthorized: Invalid or missing API key")
			return nil, errors.New("unauthorized: invalid or missing API key")
		}

		// the source_url is the shortlink that the user will be redirected from and might prefer during creation
		if len(link.SrcURL) > 0 {
			_srcURL = link.SrcURL
		} else {
			_srcURL = app.Config.GetOrDefault("SHORTLINK_BASE_URL", "https://stagedates.com/")
		}

		srcURL = _srcURL
		targetUrl = link.TargetURL
		insertedAt := time.Now()
		updatedAt := insertedAt

		query := "INSERT INTO link (src_url, target_url, inserted_at, updated_at) "
		query = query + "VALUES ($1, $2, $3, $4) "

		_, err := ctx.SQL.ExecContext(ctx,
			query,
			srcURL, targetUrl, insertedAt, updatedAt)

		if err != nil {
			ctx.Logger.Errorf("error inserting link: %+v", err)
			return nil, err
		} else {
			responseLink := DynamicLink{
				SrcURL:     srcURL,
				TargetURL:  targetUrl,
				InsertedAt: insertedAt,
				UpdatedAt:  updatedAt,
			}

			ctx.Logger.Infof("link inserted: %+v", responseLink)
			return responseLink, nil
		}

	}
}
